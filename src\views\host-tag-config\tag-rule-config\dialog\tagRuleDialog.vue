<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="visible"
    width="800px"
    :close-on-click-modal="false"
    :show-close="true"
    @close="$emit('close')"
    append-to-body
  >
    <!-- 标签规则表单 -->
    <el-form
      ref="tagRuleForm"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="left"
    >
      <el-form-item label="CDN类型" prop="cdn_type">
        <el-select
          v-model="formData.cdn_type"
          placeholder="请选择CDN类型"
          style="width: 100%"
        >
          <el-option label="PCDN" value="2"></el-option>
          <el-option label="LCDN" value="4"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="模块" prop="module">
        <el-input
          v-model="formData.module"
          placeholder="请输入模块名称"
        ></el-input>
      </el-form-item>

      <el-form-item label="标签信息" prop="tag_infos">
        <div class="tag-infos-container">
          <div
            v-for="(tag, tagIndex) in formData.tag_infos"
            :key="tagIndex"
            class="tag-row"
          >
            <el-select
              v-model="tag.tag_key"
              placeholder="请选择标签键"
              style="width: 45%; margin-right: 10px"
              @change="onTagKeyChange(tagIndex)"
              filterable
            >
              <el-option
                v-for="tagOption in tagKeyOptions"
                :key="tagOption.tag_key"
                :label="tagOption.tag_key"
                :value="tagOption.tag_key"
              ></el-option>
            </el-select>
            <el-select
              v-model="tag.tag_value"
              placeholder="请选择标签值"
              style="width: 45%; margin-right: 10px"
              filterable
            >
              <el-option
                v-for="value in getTagValueOptions(tag.tag_key)"
                :key="value"
                :label="value"
                :value="value"
              ></el-option>
            </el-select>
            <el-button
              v-if="formData.tag_infos.length > 1"
              type="danger"
              size="mini"
              @click="removeTagInfo(tagIndex)"
              icon="el-icon-delete"
            ></el-button>
          </div>
          <el-button
            type="text"
            size="small"
            @click="addTagInfo"
            icon="el-icon-plus"
          >
            添加标签
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="资源分组" prop="resource_group">
        <el-select
          v-model="formData.resource_group"
          placeholder="请选择资源分组"
          style="width: 100%"
          filterable
        >
          <el-option
            v-for="item in resourceGroupOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="RAP标签" prop="rap_tag_infos">
        <el-select
          v-model="formData.rap_tag_infos"
          placeholder="请选择RAP标签"
          style="width: 100%"
          filterable
          multiple
        >
          <el-option
            v-for="tag in bizLabelOptions"
            :key="tag.value"
            :label="tag.label"
            :value="tag.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="LVS模式" prop="lvs_mode">
        <el-select
          v-model="formData.lvs_mode"
          placeholder="请选择LVS模式"
          style="width: 100%"
        >
          <el-option label="普通模式" :value="1"></el-option>
          <el-option label="隧道模式" :value="2"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="端口" prop="port">
        <el-input
          v-model="formData.port"
          placeholder="请输入端口号，多个端口用英文逗号分隔，如：80,443,8080"
          @blur="handlePortBlur"
          @input="handlePortInput"
        ></el-input>
        <div v-if="portValidationMessage" class="port-validation-message">
          {{ portValidationMessage }}
        </div>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          placeholder="请输入备注信息"
          type="textarea"
          :rows="3"
        ></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button type="primary" :loading="submitting" @click="onSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from "@/views/host-tag-config/http.js"

export default {
  name: "tag-rule-dialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    currentRowData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      submitting: false,
      tagKeyOptions: [], // 标签键选项
      tagValueMap: {}, // 标签键对应的标签值映射
      resourceGroupOptions: [], // 资源分组选项
      bizLabelOptions: [], // 业务标签选项
      portValidationMessage: '', // 端口验证提示信息
      // 表单数据
      formData: {
        id: null,
        cdn_type: '',
        module: '',
        tag_infos: [{ tag_key: '', tag_value: '' }],
        resource_group: '',
        rap_tag_infos: [],
        lvs_mode: '',
        port: '',
        remark: ''
      },
      // 表单验证规则
      formRules: {
        cdn_type: [
          { required: true, message: '请选择CDN类型', trigger: 'change' }
        ],
        module: [
          { required: true, message: '请输入模块名称', trigger: 'blur' }
        ],
        tag_infos: [
          { required: true, validator: this.validateTagInfos, trigger: 'blur' }
        ],
        port: [
          { validator: this.validatePorts, trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '修改标签规则' : '新增标签规则';
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm();
        this.loadTagOptions();
        this.loadResourceGroupOptions();
        this.loadBizLabelOptions();
      }
    }
  },
  methods: {
    // 初始化表单数据
    initForm() {
      if (this.isEdit && this.currentRowData.id) {
        // 编辑模式，填充当前数据
        this.formData = this.createFormFromData(this.currentRowData);
      } else {
        // 新增模式，初始化空表单
        this.formData = this.createEmptyForm();
      }
      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.tagRuleForm) {
          this.$refs.tagRuleForm.clearValidate();
        }
      });
    },

    // 从现有数据创建表单对象
    createFormFromData(data) {
      // 处理端口数据：如果是数组格式，转换为逗号分隔的字符串
      let portValue = '';
      if (data.port) {
        if (Array.isArray(data.port)) {
          portValue = data.port.join(',');
        } else {
          portValue = String(data.port);
        }
      }

      return {
        id: data.id,
        cdn_type: data.cdn_type || '',
        module: data.module || '',
        tag_infos: data.tag_infos && data.tag_infos.length
          ? JSON.parse(JSON.stringify(data.tag_infos))
          : [{ tag_key: '', tag_value: '' }],
        resource_group: data.resource_group || '',
        rap_tag_infos: Array.isArray(data.rap_tag_infos)
          ? [...data.rap_tag_infos]
          : (data.rap_tag_infos ? data.rap_tag_infos.split(',').map(tag => tag.trim()).filter(tag => tag) : []),
        lvs_mode: data.lvs_mode || '',
        port: portValue,
        remark: data.remark || ''
      };
    },

    // 创建空表单对象
    createEmptyForm() {
      return {
        id: null,
        cdn_type: '',
        module: '',
        tag_infos: [{ tag_key: '', tag_value: '' }],
        resource_group: '',
        rap_tag_infos: [],
        lvs_mode: '',
        port: '',
        remark: ''
      };
    },

    // 添加标签信息
    addTagInfo() {
      this.formData.tag_infos.push({ tag_key: '', tag_value: '' });
    },

    // 移除标签信息
    removeTagInfo(tagIndex) {
      if (this.formData.tag_infos.length > 1) {
        this.formData.tag_infos.splice(tagIndex, 1);
      }
    },

    // 标签键改变时清空对应的标签值
    onTagKeyChange(tagIndex) {
      this.formData.tag_infos[tagIndex].tag_value = '';
    },

    // 自定义验证标签信息
    validateTagInfos(rule, value, callback) {
      if (!value || value.length === 0) {
        callback(new Error('请至少添加一个标签'));
        return;
      }

      const hasEmptyTag = value.some(tag => !tag.tag_key || !tag.tag_value);
      if (hasEmptyTag) {
        callback(new Error('请完善标签信息，标签键和标签值不能为空'));
        return;
      }

      callback();
    },

    // 自定义验证端口
    validatePorts(rule, value, callback) {
      if (!value || value.trim() === '') {
        callback(); // 端口不是必填项，允许为空
        return;
      }

      const result = this.validatePortString(value);
      if (!result.isValid) {
        callback(new Error(result.message));
        return;
      }

      callback();
    },

    // 验证端口字符串的格式和有效性
    validatePortString(portString) {
      if (!portString || portString.trim() === '') {
        return { isValid: true, ports: [], message: '' };
      }

      // 去除空格并分割
      const portArray = portString.replace(/\s+/g, '').split(',').filter(port => port !== '');

      if (portArray.length === 0) {
        return { isValid: true, ports: [], message: '' };
      }

      const validPorts = [];
      const invalidPorts = [];
      const duplicatePorts = [];

      for (const port of portArray) {
        // 检查是否为有效数字
        if (!/^\d+$/.test(port)) {
          invalidPorts.push(port);
          continue;
        }

        const portNum = parseInt(port, 10);

        // 检查端口范围
        if (portNum < 1 || portNum > 65535) {
          invalidPorts.push(port);
          continue;
        }

        // 检查重复
        if (validPorts.includes(portNum)) {
          if (!duplicatePorts.includes(portNum)) {
            duplicatePorts.push(portNum);
          }
        } else {
          validPorts.push(portNum);
        }
      }

      // 生成错误信息
      if (invalidPorts.length > 0) {
        return {
          isValid: false,
          ports: validPorts,
          message: `端口号必须在1-65535范围内，无效端口：${invalidPorts.join(', ')}`
        };
      }

      if (duplicatePorts.length > 0) {
        return {
          isValid: false,
          ports: validPorts,
          message: `不允许重复的端口号：${duplicatePorts.join(', ')}`
        };
      }

      return {
        isValid: true,
        ports: validPorts,
        message: ''
      };
    },

    // 处理端口输入事件
    handlePortInput(value) {
      // 实时验证并显示提示信息
      const result = this.validatePortString(value);
      if (!result.isValid) {
        this.portValidationMessage = result.message;
      } else {
        this.portValidationMessage = '';
      }
    },

    // 处理端口失焦事件
    handlePortBlur() {
      // 清理端口字符串：去除多余空格，规范化格式
      if (this.formData.port) {
        const cleanedPorts = this.formData.port
          .replace(/\s+/g, '') // 去除所有空格
          .split(',')
          .filter(port => port !== '') // 过滤空字符串
          .join(',');

        this.formData.port = cleanedPorts;

        // 重新验证
        const result = this.validatePortString(cleanedPorts);
        if (!result.isValid) {
          this.portValidationMessage = result.message;
        } else {
          this.portValidationMessage = '';
        }
      } else {
        this.portValidationMessage = '';
      }
    },
    // 加载资源分组选项
    async loadResourceGroupOptions() {
      try {
        const res = await http.getResourceGroupOptions();
        if (res) {
          this.resourceGroupOptions = res?.map(itm => ({
            label: itm.bk_inst_name,
            biz_type: itm.biz_type,
            value: `${itm.group_id}`
          })) || [];
        }
      } catch (error) {
        console.error('加载资源分组选项失败:', error);
      }
    },

    // 加载业务标签选项
    async loadBizLabelOptions() {
      try {
        const res = await http.getBizLabelOptions();
        if (res) {
          this.bizLabelOptions = res?.map(itm => ({
            label: itm.tagCnName,
            value: itm.tagCode
          })) || [];
        }
      } catch (error) {
        console.error('加载业务标签选项失败:', error);
      }
    },

    // 加载标签选项
    async loadTagOptions() {
      try {
        // 获取标签配置列表，用于联动选择
        const params = {
          page_size: 10000  // 设置足够大的值以获取全量数据
        };
        const res = await http.getTagList(params);
        if (res) {
          this.tagKeyOptions = res.data.items;

          // 构建标签键值映射
          this.tagValueMap = {};
          res.data.items.forEach(item => {
            if (item.tag_key && item.tag_value_list) {
              this.tagValueMap[item.tag_key] = item.tag_value_list;
            }
          });
        }
      } catch (error) {
        console.error('加载标签选项失败:', error);
      }
    },

    // 获取指定标签键的标签值选项
    getTagValueOptions(tagKey) {
      return this.tagValueMap[tagKey] || [];
    },
    // 提交表单
    async onSubmit() {
      // 验证表单
      const valid = await this.$refs.tagRuleForm.validate().catch(() => false);
      if (!valid) {
        return;
      }

      this.submitting = true;
      try {
        await this.handleSubmit();
      } catch (error) {
        console.error('提交失败:', error);
        this.$message.error('提交失败，请重试');
      } finally {
        this.submitting = false;
      }
    },

    // 处理提交逻辑
    async handleSubmit() {
      const operator = window.localStorage.getItem('userInfo') || 'system';

      if (this.isEdit && this.formData.id) {
        // 修改操作
        await this.handleUpdate(operator);
      } else {
        // 新增操作
        await this.handleCreate(operator);
      }
    },
    // 处理端口数据格式
    formatPortData(portString) {
      if (!portString || portString.trim() === '') {
        return '';
      }

      // 根据后端API要求，这里可以返回字符串或数组格式
      // 当前返回逗号分隔的字符串格式，如需数组格式可修改为：
      // return portString.split(',').map(port => parseInt(port.trim(), 10)).filter(port => !isNaN(port));
      return portString.replace(/\s+/g, '');
    },

    // 处理新增操作
    async handleCreate(operator) {
      const data = {
        cdn_type: this.formData.cdn_type,
        module: this.formData.module,
        tag_infos: this.formData.tag_infos,
        resource_group: this.formData.resource_group || '',
        rap_tag_infos: Array.isArray(this.formData.rap_tag_infos)
          ? this.formData.rap_tag_infos.join(",")
          : '',
        lvs_mode: this.formData.lvs_mode || '',
        port: this.formatPortData(this.formData.port),
        remark: this.formData.remark || '',
        operator
      };

      await http.addTagRule(data);
      this.$message.success('新增成功');
      this.$emit('refresh');
      this.$emit('close');
    },

    // 处理修改操作
    async handleUpdate(operator) {
      const data = {
        id: this.formData.id,
        cdn_type: this.formData.cdn_type,
        module: this.formData.module,
        tag_infos: this.formData.tag_infos,
        resource_group: this.formData.resource_group || '',
        rap_tag_infos: Array.isArray(this.formData.rap_tag_infos)
          ? this.formData.rap_tag_infos.join(",")
          : '',
        lvs_mode: this.formData.lvs_mode || '',
        port: this.formatPortData(this.formData.port),
        remark: this.formData.remark || '',
        operator
      };

      await http.updateTagRule(data);
      this.$message.success('修改成功');
      this.$emit('refresh');
      this.$emit('close');
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

.tag-infos-container {
  .tag-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 表单样式优化
.el-form {
  .el-form-item {
    margin-bottom: 20px;
  }

  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

// 标签信息区域样式
.tag-infos-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;

  .tag-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    &:last-of-type {
      margin-bottom: 15px;
    }
  }
}

// 端口验证提示信息样式
.port-validation-message {
  margin-top: 5px;
  font-size: 12px;
  color: #f56c6c;
  line-height: 1.4;
}
</style>
